import { Suspense, useState, useMemo } from "react";
import { useRouter, useNavigate } from "@tanstack/react-router";
import { routeList } from "@/lib/route-list";
import {
	ChevronLeft,
	MoreHorizontal,
	AlertTriangle,
	Bookmark,
	ChevronDown,
	ChevronUp,
	Send,
} from "react-feather";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { subjects } from "@/features/learning/constants";
import { Test } from "@/features/mcqs/types";
import { MCQPlaceholder } from "@/components/layout/mcqs/mcq-placeholder";
import MobileTestOptions from "@/components/layout/mcqs/mobile-test-option";
import { Timer } from "@/components/ui/timer";
import { useToast } from "@/hooks/use-toast";
import { useG<PERSON><PERSON><PERSON>Status, use<PERSON>QChatbot } from "@/lib/queries/chatbot.query";
import GrokKeyBanner from "@/components/chatbot/grok-key-banner";
import Latex from "react-latex-next";
import "katex/dist/katex.min.css";
import { toggleMCQType } from "@/lib/utils";
import { submitQuiz } from "@/features/tests/services";
import ReportMCQDialog from "./report-mcq-dialog";
import { addBookmark, deleteBookmark } from "@/features/bookmarks/services";
import { SubmitQuizPayload } from "@/features/tests/types";

type MCQMobileLayoutProps = {
	test: Test;
	liveCheckEnabled: boolean;
	setLiveCheckEnabled: (enabled: boolean) => void;
	selectedAnswers: Record<string, number>;
	showResults: boolean;
	onAnswerSelect: (questionId: string, optionIndex: number) => void;
	onCheckAnswers: () => void;
	bookmarkList: any;
	setRefetchBookmarks: any;
};

// Define a type for the subject IDs based on the subjects constant
type SubjectId = (typeof subjects)[number]["id"];

const MCQMobileLayout = ({
	test,
	liveCheckEnabled,
	setLiveCheckEnabled,
	selectedAnswers,
	showResults,
	onAnswerSelect,
	onCheckAnswers,
	bookmarkList,
	setRefetchBookmarks,
}: MCQMobileLayoutProps) => {
	// Get all subjects that have MCQs in the test, sorted by tab order
	const availableSubjects = useMemo(() => {
		// Get unique subjects from MCQs
		const uniqueSubjects = [...new Set(test.mcqs.map((mcq) => mcq.subject))];

		// Sort according to the order in the global subjects constant
		return subjects
			.filter((subject) => uniqueSubjects.includes(subject.id))
			.map((subject) => subject.id);
	}, [test.mcqs]);

	const defaultSubject =
		availableSubjects.length > 0 ? availableSubjects[0] : subjects[0].id;

	// State to track the currently selected subject
	const [activeSubject, setActiveSubject] = useState<SubjectId>(
		defaultSubject as SubjectId
	);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [startTime] = useState(new Date());
	const [isReportDialogOpen, setIsReportDialogOpen] = useState(false);
	const [reportingMcqId, setReportingMcqId] = useState<string>("");
	const [showExpertAnswer, setShowExpertAnswer] = useState<Record<string, boolean>>({});
	const [showChatbot, setShowChatbot] = useState<Record<string, boolean>>({});
	const [chatbotQuestions, setChatbotQuestions] = useState<Record<string, string>>({});
	const [chatbotResponses, setChatbotResponses] = useState<Record<string, string>>({});

	const { toast } = useToast();
	const navigate = useNavigate();

	// Grok key status and chatbot mutation
	const { data: grokKeyStatus } = useGrokKeyStatus();
	const chatbotMutation = useMCQChatbot();

	// Parse test duration to get minutes and seconds for the timer
	const parseTestDuration = () => {
		try {
			// Split duration into hours, minutes, seconds
			const parts = test.duration.split(":");
			// Extract hours, minutes, seconds - use nullish coalescing to ensure we have valid numbers
			const hours = parseInt(parts[0] ?? "0", 10);
			const minutes = parseInt(parts[1] ?? "0", 10);
			const seconds = parseInt(parts[2] ?? "0", 10);

			// Calculate total minutes for the timer
			return {
				minutes: hours * 60 + minutes,
				seconds: seconds,
			};
		} catch (error) {
			console.error("Error parsing test duration:", error);
			return { minutes: 30, seconds: 0 }; // Default to 30 minutes if parsing fails
		}
	};

	const { minutes, seconds } = parseTestDuration();

	const handleTimeEnd = () => {
		toast({
			title: "Time's up!",
			description: "Your test time has ended. Please submit your answers.",
			variant: "destructive",
		});
		// Auto-submit when time expires
		handleSubmitQuiz();
	};

	// Filter MCQs based on selected subject
	const filteredMCQs = test.mcqs.filter((mcq) => mcq.subject === activeSubject);

	// Group MCQs by subject based on the tab order
	const mcqsBySubject = useMemo(() => {
		const grouped: Record<string, typeof test.mcqs> = {};

		// Group MCQs by subject in the tab display order
		availableSubjects.forEach((subject) => {
			grouped[subject] = test.mcqs.filter((mcq) => mcq.subject === subject);
		});

		return grouped;
	}, [test.mcqs, availableSubjects]);

	// Create a mapping of question IDs to their sequential numbers based on tab order
	const questionNumberMap = useMemo(() => {
		const map = new Map<string, number>();
		let counter = 1;

		// For each subject in the tab order
		availableSubjects.forEach((subject) => {
			// Get questions for this subject
			const subjectMCQs = mcqsBySubject[subject] || [];

			// Assign sequential numbers
			subjectMCQs.forEach((mcq) => {
				map.set(mcq.id, counter++);
			});
		});

		return map;
	}, [mcqsBySubject, availableSubjects]);

	const { history } = useRouter();
	const pathname = window.location.pathname;
	const currentRoute = routeList
		.find((menu) => menu.menus.some((m) => m.href === pathname))
		?.menus.find((m) => m.href === pathname);

	const label = currentRoute?.label;

	const handleClick = () => {
		history.go(-1);
	};

	const handleTabClick = (subjectId: string) => {
		setActiveSubject(subjectId as SubjectId);
	};

	const handleReportMCQ = (mcqId: string) => {
		setReportingMcqId(mcqId);
		setIsReportDialogOpen(true);
	};

	// Bookmark functions
	const isBookmarked = (mcqId: string) => {
		return bookmarkList?.mcq?.mcqs?.filter((item: any) => item?._id === mcqId)
			.length
			? true
			: false;
	};

	const removeBookmark = async (mcqId: string) => {
		const response = await deleteBookmark({ category: "mcq", id: mcqId });
		console.log("removeBookmark", response);
		if (response?.data?.status === 200 && response?.data?.success) {
			toast({
				title: "Success",
				description: "Bookmark removed!",
			});
		} else {
			toast({
				title: "Error",
				description: "Failed to remove the bookmark",
				variant: "destructive",
			});
		}
	};

	const saveBookmark = async (mcqId: string) => {
		const response = await addBookmark({ category: "mcq", id: mcqId });
		console.log("saveBookmark", response);
		if (response?.data?.status === 200 && response?.data?.success) {
			toast({
				title: "Success",
				description: "MCQ bookmarked!",
			});
		} else {
			toast({
				title: "Error",
				description: "Failed to bookmark the MCQ",
				variant: "destructive",
			});
		}
	};

	const toggleBookmark = async (mcqId: string) => {
		if (isBookmarked(mcqId)) {
			await removeBookmark(mcqId);
		} else {
			await saveBookmark(mcqId);
		}
		setRefetchBookmarks((prev: any) => !prev);
	};

	const handleChatbotSubmit = async (mcqId: string) => {
		const question = chatbotQuestions[mcqId];
		if (!question?.trim()) {
			toast({
				title: "Error",
				description: "Please enter a question",
				variant: "destructive",
			});
			return;
		}

		const mcq = test.mcqs.find(m => m.id === mcqId);
		if (!mcq) return;

		const userChoice = selectedAnswers[mcqId] !== undefined
			? `${String.fromCharCode(65 + selectedAnswers[mcqId])}.${mcq.options[selectedAnswers[mcqId]]}`
			: "No answer selected";

		const correctAnswer = `${String.fromCharCode(65 + mcq.correctAnswer)}.${mcq.options[mcq.correctAnswer]}`;

		const payload = {
			mcqid: mcq.id,
			mcqTitle: mcq.question,
			options: mcq.options.map((option, index) => `${String.fromCharCode(65 + index)}.${option}`),
			userChoice,
			correctAnswer,
			explanation: mcq.description || "",
			question,
		};

		try {
			const response = await chatbotMutation.mutateAsync(payload);
			setChatbotResponses(prev => ({ ...prev, [mcqId]: response.data.response }));
			setChatbotQuestions(prev => ({ ...prev, [mcqId]: "" })); // Clear the input after successful submission
		} catch (error: any) {
			toast({
				title: "Error",
				description: error?.response?.data?.message || "Failed to get chatbot response",
				variant: "destructive",
			});
		}
	};

	// Function to calculate time taken in seconds
	const getTimeTakenInSeconds = (): number => {
		const end = new Date();
		const diffMs = end.getTime() - startTime.getTime();
		return Math.floor(diffMs / 1000);
	};

	// Handle quiz submission
	const handleSubmitQuiz = async () => {
		try {
			setIsSubmitting(true);
			// Include all MCQs in submission, even unanswered ones (as null/undefined)
			const payload: SubmitQuizPayload = {
				quizId: test.id,
				timeTaken: getTimeTakenInSeconds(),
				chosenOptions: test.mcqs.map((mcq) => ({
					mcqId: mcq.id,
					chosenOption: selectedAnswers[mcq.id] ?? 0, // Use 0 as default for unanswered
				})),
			};

			const response = await submitQuiz(payload);
			console.log("Mobile submission response:", response);

			if (response.data && response.data.success) {
				onCheckAnswers(); // This will trigger showing results
			} else {
				throw new Error("Failed to submit quiz");
			}
		} catch (error) {
			console.error("Failed to submit quiz:", error);
			toast({
				title: "Error",
				description: "Failed to submit quiz. Please try again.",
				variant: "destructive",
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	// Handle continue to practice button
	const handleContinueToPractice = () => {
		navigate({ to: "/t/mock" });
	};

	return (
		<div className="flex flex-col h-screen bg-gray-50 overflow-hidden">
			<div className="bg-accent text-white p-4">
				<div className="flex items-center justify-between my-4 py-4">
					<div className="flex items-center text-center text gap-2">
						<ChevronLeft className="w-6 h-6" onClick={handleClick} />
					</div>
					<h2 className="font-medium">{test.title ?? label}</h2>
					<div className="flex items-center gap-2 text-sm bg-white text-accent py-1.5 px-2 rounded-full">
						<Timer
							initialMinutes={minutes}
							initialSeconds={seconds}
							onTimeEnd={handleTimeEnd}
							quizSubmitted={showResults}
							className="text-accent"
						/>
					</div>

					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button variant="ghost" size="icon" className="text-white">
								<MoreHorizontal className="w-5 h-5" />
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align="end" className="w-60 p-3">
							<MobileTestOptions
								liveCheckEnabled={liveCheckEnabled}
								setLiveCheckEnabled={setLiveCheckEnabled}
								showResults={showResults}
								test={test}
								bookmarkList={bookmarkList}
								setRefetchBookmarks={setRefetchBookmarks}
							/>
						</DropdownMenuContent>
					</DropdownMenu>
				</div>

				<div className="overflow-x-auto pb-2">
					<div className="flex gap-2">
						{subjects
							.filter((subject) => availableSubjects.includes(subject.id))
							.map((subject) => (
								<Button
									key={subject.id}
									onClick={() => handleTabClick(subject.id)}
									className={`whitespace-nowrap rounded-full px-4 py-2 text-sm font-medium ${
										activeSubject === subject.id
											? "bg-white text-accent"
											: "bg-accent/20 text-white"
									}`}
								>
									{subject.label}
								</Button>
							))}
					</div>
				</div>
			</div>

			<div className="flex-1 overflow-y-auto px-4 py-2 min-h-0">
				{filteredMCQs.length > 0 ? (
					filteredMCQs.map((mcq) => {
						const hasAnswered = selectedAnswers[mcq.id] !== undefined;

						return (
							<div key={mcq.id} className="mcq-container mb-6">
								<Suspense fallback={<MCQPlaceholder />}>
									<div className="bg-white rounded-lg p-4 shadow-sm border">
										{/* Question Header */}
										<div className="flex items-center justify-between mb-4">
											<div className="flex items-center gap-2">
												<p className="text-gray-500 font-bold">
													QUESTION #{questionNumberMap.get(mcq.id) || mcq.id}
												</p>
												{mcq.tag && (
													<span className="text-sm px-2 py-0.5 font-semibold border border-purple-300 bg-purple-100 text-accent rounded-full">
														{toggleMCQType(mcq.tag)}
													</span>
												)}
												{mcq.difficulty && (
													<span
														className={`text-sm px-2 py-0.5 font-semibold rounded-full border ${
															mcq.difficulty === "easy"
																? "border-green-300 bg-green-100 text-green-800"
																: mcq.difficulty === "medium"
																	? "border-yellow-300 bg-yellow-100 text-yellow-800"
																	: mcq.difficulty === "hard"
																		? "border-red-300 bg-red-100 text-red-800"
																		: ""
														}`}
													>
														{mcq.difficulty}
													</span>
												)}
											</div>
											<div className="flex items-center gap-2">
												<Button
													variant="ghost"
													size="sm"
													onClick={() => toggleBookmark(mcq.id)}
													className="p-2"
													title={
														isBookmarked(mcq.id)
															? "Remove bookmark"
															: "Bookmark this MCQ"
													}
												>
													<Bookmark
														className={`w-4 h-4 ${
															isBookmarked(mcq.id)
																? "fill-accent text-accent"
																: "text-gray-500 hover:text-accent"
														}`}
													/>
												</Button>
												<Button
													variant="ghost"
													size="sm"
													onClick={() => handleReportMCQ(mcq.id)}
													className="p-2"
													title="Report this MCQ"
												>
													<AlertTriangle className="w-4 h-4 text-gray-500 hover:text-red-500" />
												</Button>
											</div>
										</div>

										{/* Question Text */}
										<h3 className="mb-6 text-lg font-medium">
											<Latex>{mcq.question}</Latex>
										</h3>

										{/* Answer Options */}
										<div className="space-y-3">
											<p className="font-bold text-gray-400 mb-4">
												CHOOSE ANSWER
											</p>
											{mcq.options.map((option, index) => {
												const isSelected = selectedAnswers[mcq.id] === index;
												const isCorrect = index === mcq.correctAnswer;
												const showFeedback =
													(liveCheckEnabled && hasAnswered) || showResults;

												// Live check behavior: disable changing answers once selected
												const isDisabled =
													showResults || (liveCheckEnabled && hasAnswered);

												let buttonClass =
													"w-full p-4 text-left border rounded-lg transition-colors ";

												if (showFeedback) {
													if (isCorrect) {
														buttonClass +=
															"bg-green-100 border-green-500 text-green-800";
													} else if (isSelected && !isCorrect) {
														buttonClass +=
															"bg-red-100 border-red-500 text-red-800";
													} else {
														buttonClass +=
															"bg-gray-50 border-gray-200 text-gray-600";
													}
												} else if (isSelected) {
													buttonClass +=
														"bg-blue-100 border-blue-500 text-blue-800";
												} else {
													buttonClass +=
														"bg-white border-gray-200 text-gray-700 hover:bg-gray-50";
												}

												// Add disabled styling
												if (isDisabled && !isSelected && !isCorrect) {
													buttonClass += " opacity-50 cursor-not-allowed";
												}

												return (
													<button
														key={index}
														onClick={() =>
															!isDisabled && onAnswerSelect(mcq.id, index)
														}
														disabled={isDisabled}
														className={buttonClass}
													>
														<div className="flex items-start gap-3">
															<span className="font-bold text-sm mt-1">
																{String.fromCharCode(65 + index)}.
															</span>
															<span className="flex-1">
																<Latex>{option}</Latex>
															</span>
															{showFeedback && isCorrect && (
																<span className="text-xs bg-green-500 text-white px-2 py-1 rounded">
																	Correct
																</span>
															)}
															{showFeedback && isSelected && !isCorrect && (
																<span className="text-xs bg-red-500 text-white px-2 py-1 rounded">
																	Wrong
																</span>
															)}
														</div>
													</button>
												);
											})}
										</div>

										{/* Live Check Feedback Section - Show immediately after selection */}
										{liveCheckEnabled && hasAnswered && !showResults && (
											<div className="mt-4">
												<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
													<div
														className={`py-2 px-4 rounded-full max-w-[90%] ${
															selectedAnswers[mcq.id] === mcq.correctAnswer
																? "bg-green-100 text-green-700"
																: "bg-red-100 text-red-700"
														} flex items-center gap-2`}
													>
														<span className="text-sm">
															{selectedAnswers[mcq.id] === mcq.correctAnswer
																? "You have chosen the correct answer."
																: "You have chosen the wrong answer."}
														</span>
													</div>
													<Button
														variant="link"
														onClick={() => setShowExpertAnswer(prev => ({ ...prev, [mcq.id]: !prev[mcq.id] }))}
														className="justify-start text-gray-600 flex items-center gap-1 text-sm"
													>
														{showExpertAnswer[mcq.id] ? "Hide" : "View"} expert answer
														{showExpertAnswer[mcq.id] ? (
															<ChevronUp size={16} />
														) : (
															<ChevronDown size={16} />
														)}
													</Button>
												</div>

												{showExpertAnswer[mcq.id] && (
													<div className="mt-2 p-4 bg-gray-50 rounded-md">
														<p className="text-sm">
															<Latex>{String(mcq.description)}</Latex>
														</p>
														{selectedAnswers[mcq.id] !== mcq.correctAnswer && (
															<div className="mt-2 pt-2 border-t border-gray-200">
																<p className="text-sm font-medium text-green-600">
																	Correct answer:{" "}
																	{String.fromCharCode(65 + mcq.correctAnswer)} -{" "}
																	<Latex>
																		{String(mcq.options[mcq.correctAnswer] ?? "")}
																	</Latex>
																</p>
															</div>
														)}
													</div>
												)}

												{/* Ask Chatbot Section - Only for wrong answers and when expert answer is shown */}
												{showExpertAnswer[mcq.id] && selectedAnswers[mcq.id] !== mcq.correctAnswer && (
													<div className="mt-4">
														{/* Ask Chatbot Button */}
														<div className="mb-4">
															<Button
																variant="outline"
																onClick={() => setShowChatbot(prev => ({ ...prev, [mcq.id]: !prev[mcq.id] }))}
																className="flex items-center gap-2 text-purple-600 border-purple-200 hover:bg-purple-50"
															>
																<span className="text-lg">🤖</span>
																Ask Chatbot
															</Button>
														</div>

														{/* Chatbot Input Section */}
														{showChatbot[mcq.id] && (
															<div className="space-y-4">
																{/* Grok Key Banner */}
																{!chatbotResponses[mcq.id] && (
																	<GrokKeyBanner
																		hasGrokKey={grokKeyStatus?.has_groq_api_key || false}
																		className="mb-4"
																	/>
																)}

																{/* Chatbot */}
																<div className="rounded-md">
																	{chatbotResponses[mcq.id] && (
																		<div className="flex items-start gap-3 mb-4 border-b border-gray-200 pb-4">
																			<div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
																				<span className="text-sm">🤖</span>
																			</div>
																			<div className="flex-1">
																				<p className="text-sm font-medium text-gray-800 mb-1">
																					Chatbot says...
																				</p>
																				<p className="text-sm text-gray-700 whitespace-pre-wrap">
																					{chatbotResponses[mcq.id]}
																				</p>
																			</div>
																		</div>
																	)}

																	{/* Ask Chatbot input */}
																	<div className="flex gap-2">
																		<Input
																			placeholder="Ask Chatbot..."
																			value={chatbotQuestions[mcq.id] || ""}
																			onChange={(e) => setChatbotQuestions(prev => ({ ...prev, [mcq.id]: e.target.value }))}
																			disabled={chatbotMutation.isPending || !grokKeyStatus?.has_groq_api_key}
																			className="flex-1"
																			onKeyDown={(e) => {
																				if (e.key === 'Enter' && grokKeyStatus?.has_groq_api_key) {
																					handleChatbotSubmit(mcq.id);
																				}
																			}}
																		/>
																		<Button
																			onClick={() => handleChatbotSubmit(mcq.id)}
																			disabled={chatbotMutation.isPending || !chatbotQuestions[mcq.id]?.trim() || !grokKeyStatus?.has_groq_api_key}
																			className="bg-purple-600 hover:bg-purple-700"
																		>
																			{chatbotMutation.isPending ? (
																				<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
																			) : (
																				<Send size={16} />
																			)}
																		</Button>
																	</div>
																</div>
															</div>
														)}
													</div>
												)}
											</div>
										)}

										{/* Final Results Feedback Section - Show after submission for ALL MCQs */}
										{showResults && (
											<div className="mt-4">
												<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
													<div
														className={`py-2 px-4 rounded-full max-w-[90%] ${
															selectedAnswers[mcq.id] === mcq.correctAnswer
																? "bg-green-100 text-green-700"
																: selectedAnswers[mcq.id] !== undefined
																	? "bg-red-100 text-red-700"
																	: "bg-gray-100 text-gray-700"
														} flex items-center gap-2`}
													>
														<span className="text-sm">
															{selectedAnswers[mcq.id] === mcq.correctAnswer
																? "You have chosen the correct answer."
																: selectedAnswers[mcq.id] !== undefined
																	? "You have chosen the wrong answer."
																	: "You have not answered this question."}
														</span>
													</div>
													<Button
														variant="link"
														onClick={() => setShowExpertAnswer(prev => ({ ...prev, [mcq.id]: !prev[mcq.id] }))}
														className="justify-start text-gray-600 flex items-center gap-1 text-sm"
													>
														{showExpertAnswer[mcq.id] ? "Hide" : "View"} expert answer
														{showExpertAnswer[mcq.id] ? (
															<ChevronUp size={16} />
														) : (
															<ChevronDown size={16} />
														)}
													</Button>
												</div>

												{showExpertAnswer[mcq.id] && (
													<div className="mt-2 p-4 bg-gray-50 rounded-md">
														<p className="text-sm">
															<Latex>{String(mcq.description)}</Latex>
														</p>
														{selectedAnswers[mcq.id] !== mcq.correctAnswer && (
															<div className="mt-2 pt-2 border-t border-gray-200">
																<p className="text-sm font-medium text-green-600">
																	Correct answer:{" "}
																	{String.fromCharCode(65 + mcq.correctAnswer)} -{" "}
																	<Latex>
																		{String(mcq.options[mcq.correctAnswer] ?? "")}
																	</Latex>
																</p>
															</div>
														)}
													</div>
												)}

												{/* Ask Chatbot Section - Only for wrong answers and when expert answer is shown */}
												{showExpertAnswer[mcq.id] && selectedAnswers[mcq.id] !== mcq.correctAnswer && (
													<div className="mt-4">
														{/* Ask Chatbot Button */}
														<div className="mb-4">
															<Button
																variant="outline"
																onClick={() => setShowChatbot(prev => ({ ...prev, [mcq.id]: !prev[mcq.id] }))}
																className="flex items-center gap-2 text-purple-600 border-purple-200 hover:bg-purple-50"
															>
																<span className="text-lg">🤖</span>
																Ask Chatbot
															</Button>
														</div>

														{/* Chatbot Input Section */}
														{showChatbot[mcq.id] && (
															<div className="space-y-4">
																{/* Grok Key Banner */}
																{!chatbotResponses[mcq.id] && (
																	<GrokKeyBanner
																		hasGrokKey={grokKeyStatus?.has_groq_api_key || false}
																		className="mb-4"
																	/>
																)}

																{/* Chatbot */}
																<div className="rounded-md">
																	{chatbotResponses[mcq.id] && (
																		<div className="flex items-start gap-3 mb-4 border-b border-gray-200 pb-4">
																			<div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
																				<span className="text-sm">🤖</span>
																			</div>
																			<div className="flex-1">
																				<p className="text-sm font-medium text-gray-800 mb-1">
																					Chatbot says...
																				</p>
																				<p className="text-sm text-gray-700 whitespace-pre-wrap">
																					{chatbotResponses[mcq.id]}
																				</p>
																			</div>
																		</div>
																	)}

																	{/* Ask Chatbot input */}
																	<div className="flex gap-2">
																		<Input
																			placeholder="Ask Chatbot..."
																			value={chatbotQuestions[mcq.id] || ""}
																			onChange={(e) => setChatbotQuestions(prev => ({ ...prev, [mcq.id]: e.target.value }))}
																			disabled={chatbotMutation.isPending || !grokKeyStatus?.has_groq_api_key}
																			className="flex-1"
																			onKeyDown={(e) => {
																				if (e.key === 'Enter' && grokKeyStatus?.has_groq_api_key) {
																					handleChatbotSubmit(mcq.id);
																				}
																			}}
																		/>
																		<Button
																			onClick={() => handleChatbotSubmit(mcq.id)}
																			disabled={chatbotMutation.isPending || !chatbotQuestions[mcq.id]?.trim() || !grokKeyStatus?.has_groq_api_key}
																			className="bg-purple-600 hover:bg-purple-700"
																		>
																			{chatbotMutation.isPending ? (
																				<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
																			) : (
																				<Send size={16} />
																			)}
																		</Button>
																	</div>
																</div>
															</div>
														)}
													</div>
												)}
											</div>
										)}
									</div>
								</Suspense>
							</div>
						);
					})
				) : (
					<div className="p-6 text-center">
						<p className="text-gray-500">
							No questions available for this subject.
						</p>
					</div>
				)}
			</div>

			<div className="p-4 border-t bg-white flex-shrink-0">
				{!showResults ? (
					<Button
						onClick={handleSubmitQuiz}
						className="w-full bg-accent text-white rounded-full py-6"
						disabled={
							Object.keys(selectedAnswers).length !== test.mcqs.length ||
							isSubmitting
						}
					>
						{isSubmitting
							? "Submitting..."
							: `Submit Answers (${Object.keys(selectedAnswers).length}/${test.mcqs.length})`}
					</Button>
				) : (
					<div className="flex flex-col gap-3">
						<Button
							className="w-full bg-accent text-white rounded-full py-6"
							onClick={handleContinueToPractice}
						>
							Continue to Practice
						</Button>
					</div>
				)}
			</div>

			<ReportMCQDialog
				open={isReportDialogOpen}
				onOpenChange={setIsReportDialogOpen}
				mcqId={reportingMcqId}
			/>
		</div>
	);
};

export default MCQMobileLayout;
