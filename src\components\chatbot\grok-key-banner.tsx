import React from "react";
import { Link as RouterLink } from "@tanstack/react-router";
import { ArrowRight } from "react-feather";

interface GrokKeyBannerProps {
	hasGrokKey: boolean;
	className?: string;
}

const GrokKeyBanner: React.FC<GrokKeyBannerProps> = ({ hasGrokKey, className = "" }) => {
	if (hasGrokKey) {
		return (
			<div className={`bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl p-4 text-white ${className}`}>
				<div className="flex items-center gap-3">
					<div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0">
						<span className="text-2xl">🤖</span>
					</div>
					<div className="flex-1">
						<h3 className="font-semibold text-sm">You have Unlimited</h3>
						<p className="text-purple-100 text-xs">Private Chatbot!</p>
						<RouterLink
							to="/add-key"
							className="inline-flex items-center gap-1 text-xs underline hover:no-underline mt-1"
						>
							<span>Change Grok Key</span>
							<ArrowRight size={12} />
						</RouterLink>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className={`bg-white rounded-xl p-4 border border-purple-200 shadow-sm ${className}`}>
			<div className="flex items-center gap-3">
				<div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
					<span className="text-2xl">🤖</span>
				</div>
				<div className="flex-1">
					<h3 className="font-semibold text-sm text-gray-800">Want unlimited Chatbot?</h3>
					<p className="text-gray-600 text-xs">Bring your own key!</p>
					<RouterLink
						to="/add-key"
						className="inline-flex items-center gap-1 text-purple-600 text-xs underline hover:no-underline mt-1"
					>
						<span>Add Grok Key</span>
						<ArrowRight size={12} />
					</RouterLink>
				</div>
			</div>
		</div>
	);
};

export default GrokKeyBanner;
