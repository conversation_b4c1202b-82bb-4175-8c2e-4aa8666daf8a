import axios, { type AxiosInstance } from "axios";
import { useAuthStore } from "@/features/auth/store";
import type { ChatbotStatusResponse, AddGrokKeyRequest, AddGrokKeyResponse, MCQChatbotRequest, MCQChatbotResponse } from "./types";

// Create a separate axios instance for chatbot API
const createChatbotApi = (): AxiosInstance => {
	const baseURL = import.meta.env.VITE_CHATBOT_URL || import.meta.env.VITE_CHATBOT_FALLBACK_URL;
	
	const chatbotApi = axios.create({
		baseURL,
		headers: {
			"Content-Type": "application/json",
		},
	});

	// Add request interceptor to include Firebase token
	chatbotApi.interceptors.request.use(
		(config) => {
			const firebaseToken = useAuthStore.getState().firebaseToken;
			if (firebaseToken) {
				config.headers["firebase-token"] = firebaseToken;
			}
			return config;
		},
		(error) => {
			return Promise.reject(error);
		}
	);

	return chatbotApi;
};

const chatbotApi = createChatbotApi();

export const checkGrokKeyStatus = () => {
	return chatbotApi.get<ChatbotStatusResponse>("/");
};

export const addGrokKey = (data: AddGrokKeyRequest) => {
	return chatbotApi.post<AddGrokKeyResponse>("/add-groq-key", data);
};

export const askMCQChatbot = (data: MCQChatbotRequest) => {
	return chatbotApi.post<MCQChatbotResponse>("/chatbot", data);
};
