import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { X, Send } from "lucide-react";
import YouTube, { YouTubeProps } from "react-youtube";
import { Resource } from "@/features/resources/types";
import { extractYouTubeVideoId } from "@/lib/utils";
import { useGrokKeyStatus, useMCQChatbot } from "@/lib/queries/chatbot.query";
import GrokKeyBanner from "@/components/chatbot/grok-key-banner";
import { useToast } from "@/hooks/use-toast";

interface VideoResourceViewerProps {
	resource: Resource | null;
	isOpen: boolean;
	onClose: () => void;
}

const VideoResourceViewer: React.FC<VideoResourceViewerProps> = ({
	resource,
	isOpen,
	onClose,
}) => {
	const [chatbotQuestion, setChatbotQuestion] = useState("");
	const [chatbotResponse, setChatbotResponse] = useState("");
	const { toast } = useToast();

	// Grok key status and chatbot mutation
	const { data: grokKeyStatus } = useGrokKeyStatus();
	const chatbotMutation = useMCQChatbot();

	const opts: YouTubeProps["opts"] = {
		width: "100%",
		height: "100%",
		playerVars: {
			autoplay: 0,
		},
	};

	const handleChatbotSubmit = async () => {
		if (!chatbotQuestion.trim()) {
			toast({
				title: "Error",
				description: "Please enter a question",
				variant: "destructive",
			});
			return;
		}

		if (!resource) return;

		// Create dummy MCQ-based payload for now since backend doesn't support resource chatbot yet
		const payload = {
			mcqid: resource.id,
			mcqTitle: resource.title,
			options: ["A. Option 1", "B. Option 2", "C. Option 3", "D. Option 4"], // Dummy options
			userChoice: "No answer selected",
			correctAnswer: "A. Option 1", // Dummy correct answer
			explanation: resource.description || "",
			question: chatbotQuestion,
		};

		try {
			const response = await chatbotMutation.mutateAsync(payload);
			setChatbotResponse(response.data.response);
			setChatbotQuestion(""); // Clear the input after successful submission
		} catch (error: any) {
			toast({
				title: "Error",
				description: error?.response?.data?.message || "Failed to get chatbot response",
				variant: "destructive",
			});
		}
	};

	if (!resource) return null;

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-7xl h-[90vh] p-0 overflow-hidden">
				<DialogHeader className="p-6 pb-0">
					<div className="flex items-center justify-between">
						<DialogTitle className="text-xl font-semibold">
							{resource.title}
						</DialogTitle>
						<Button
							variant="ghost"
							size="sm"
							onClick={onClose}
							className="h-8 w-8 p-0"
						>
							<X size={16} />
						</Button>
					</div>
				</DialogHeader>

				<div className="flex h-full">
					{/* Video Section */}
					<div className="flex-1 p-6 pt-0">
						<div className="relative h-96 w-full bg-black rounded-lg overflow-hidden">
							<YouTube
								className="h-full w-full"
								videoId={extractYouTubeVideoId(resource.url)}
								opts={opts}
							/>
						</div>

						{/* Tabs Section */}
						<div className="mt-6">
							<Tabs defaultValue="overview" className="w-full">
								<TabsList className="grid w-full grid-cols-2">
									<TabsTrigger value="overview">Overview</TabsTrigger>
									<TabsTrigger value="ai-chatbot">AI Chatbot</TabsTrigger>
								</TabsList>

								<TabsContent value="overview" className="mt-4">
									<div className="space-y-4">
										<div>
											<h3 className="text-lg font-semibold mb-2">About Course</h3>
											<p className="text-gray-700 leading-relaxed">
												{resource.description || "No description available for this resource."}
											</p>
										</div>

										{/* Additional course details can be added here */}
										<div>
											<h4 className="text-md font-medium mb-2">What You'll Learn</h4>
											<div className="grid grid-cols-2 gap-2">
												<div className="flex items-center gap-2">
													<span className="text-green-500">✓</span>
													<span className="text-sm">Understanding core concepts</span>
												</div>
												<div className="flex items-center gap-2">
													<span className="text-green-500">✓</span>
													<span className="text-sm">Practical applications</span>
												</div>
												<div className="flex items-center gap-2">
													<span className="text-green-500">✓</span>
													<span className="text-sm">Problem-solving techniques</span>
												</div>
												<div className="flex items-center gap-2">
													<span className="text-green-500">✓</span>
													<span className="text-sm">Advanced methodologies</span>
												</div>
											</div>
										</div>
									</div>
								</TabsContent>

								<TabsContent value="ai-chatbot" className="mt-4">
									<div className="space-y-4">
										<div className="text-center py-8">
											<h3 className="text-lg font-semibold mb-2">Start a Chat!</h3>
											<p className="text-gray-600 text-sm mb-6">
												Ask AI questions about the specific question or topic.
											</p>

											{/* Grok Key Banner - Only show when no response yet */}
											{!chatbotResponse && (
												<GrokKeyBanner
													hasGrokKey={grokKeyStatus?.has_groq_api_key || false}
													className="mb-6"
												/>
											)}

											{/* Chatbot Response */}
											{chatbotResponse && (
												<div className="flex items-start gap-3 mb-6 p-4 bg-gray-50 rounded-lg text-left">
													<div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
														<span className="text-sm">🤖</span>
													</div>
													<div className="flex-1">
														<p className="text-sm font-medium text-gray-800 mb-1">
															Chatbot says...
														</p>
														<p className="text-sm text-gray-700 whitespace-pre-wrap">
															{chatbotResponse}
														</p>
													</div>
												</div>
											)}

											{/* Chat Input */}
											<div className="flex gap-2 max-w-md mx-auto">
												<Input
													placeholder="Write a message"
													value={chatbotQuestion}
													onChange={(e) => setChatbotQuestion(e.target.value)}
													disabled={
														chatbotMutation.isPending ||
														!grokKeyStatus?.has_groq_api_key
													}
													className="flex-1"
													onKeyDown={(e) => {
														if (
															e.key === "Enter" &&
															grokKeyStatus?.has_groq_api_key
														) {
															handleChatbotSubmit();
														}
													}}
												/>
												<Button
													onClick={handleChatbotSubmit}
													disabled={
														chatbotMutation.isPending ||
														!chatbotQuestion.trim() ||
														!grokKeyStatus?.has_groq_api_key
													}
													className="bg-purple-600 hover:bg-purple-700"
												>
													{chatbotMutation.isPending ? (
														<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
													) : (
														<Send size={16} />
													)}
												</Button>
											</div>
										</div>
									</div>
								</TabsContent>
							</Tabs>
						</div>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
};

export default VideoResourceViewer;
