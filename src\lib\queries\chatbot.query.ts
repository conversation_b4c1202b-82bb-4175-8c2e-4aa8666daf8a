import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { checkGrokKeyStatus, add<PERSON><PERSON><PERSON>ey, askMCQChatbot } from "@/features/chatbot/services";
import type { AddGrokKeyRequest, MCQChatbotRequest } from "@/features/chatbot/types";

export const useGrokKeyStatus = () => {
	return useQuery({
		queryKey: ["grokKeyStatus"],
		queryFn: checkGrokKeyStatus,
		select: (data) => data.data,
		retry: 1,
		refetchOnWindowFocus: false,
	});
};

export const useAddGrokKey = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (data: AddGrokKeyRequest) => addGrokKey(data),
		onSuccess: () => {
			// Invalidate and refetch the Grok key status
			queryClient.invalidateQueries({ queryKey: ["grokKeyStatus"] });
		},
	});
};

export const useMCQChatbot = () => {
	return useMutation({
		mutationFn: (data: MCQChatbotRequest) => ask<PERSON>Q<PERSON>hatbot(data),
	});
};
