import React, { useState } from "react";
import { create<PERSON>ile<PERSON><PERSON><PERSON>, useNavigate } from "@tanstack/react-router";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAddGrokKey } from "@/lib/queries/chatbot.query";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft, Eye, EyeOff } from "react-feather";
import { ICONS } from "@/lib/assets/images";

const AddGrokKeyPage = () => {
	const [grokKey, setGrokKey] = useState("");
	const [showKey, setShowKey] = useState(false);
	const navigate = useNavigate();
	const { toast } = useToast();

	const addGrokKeyMutation = useAddGrokKey();

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!grokKey.trim()) {
			toast({
				title: "Error",
				description: "Please enter a valid Grok API key",
				variant: "destructive",
			});
			return;
		}

		try {
			await addGrokKeyMutation.mutateAsync({ groq_api_key: grokKey.trim() });
			toast({
				title: "Success",
				description: "Grok API key added successfully!",
			});
			navigate({ to: "/dashboard" });
		} catch (error: any) {
			toast({
				title: "Error",
				description:
					error?.response?.data?.message || "Failed to add Grok API key",
				variant: "destructive",
			});
		}
	};

	const handleReturn = () => {
		navigate({ to: "/dashboard" });
	};

	return (
		<div className="min-h-screen bg-gradient-to-r from-[#CBBBFF] via-[#CBBBFF20] to-[#FFFFFF00] flex flex-col p-6">
			<div className="relative w-full mb-12 pt-4">
				<div className="flex items-center text-purple-600 font-bold text-2xl ml-8">
					<img
						src={ICONS.logoexpanded}
						alt="Logo"
						className="w-32 sm:w-36 lg:w-40"
					/>
				</div>
			</div>

			{/* Main Card */}
			<div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-md bg-white rounded-2xl shadow-xl p-8">
				<div className="text-center mb-8">
					<h2 className="text-2xl font-bold text-gray-800 mb-2">
						Add Groq API Key
					</h2>
					<p className="text-gray-600 text-sm mb-1">
						Currently you're using the Public
					</p>
					<p className="text-gray-600 text-sm mb-4">Parhlai Chatbot!</p>
					<p className="text-gray-500 text-sm italic mb-4">
						Due to high demand, delays in the Chatbot can be expected...
					</p>
					<div className="text-sm text-gray-600">
						We recommend using a personal key from{" "}
						<a
							href="https://console.groq.com"
							target="_blank"
							rel="noopener noreferrer"
							className="text-purple-600 hover:text-purple-700 underline"
						>
							Groq
						</a>
						, to
					</div>
					<div className="text-sm text-gray-600 mb-2">
						unlock unlimited and private chatbot service!
					</div>
					<div className="text-purple-600 font-semibold text-sm">
						And it's free!
					</div>
				</div>

				<form onSubmit={handleSubmit} className="space-y-6">
					<div className="relative">
						<Input
							type={showKey ? "text" : "password"}
							placeholder="Enter your Groq API key"
							value={grokKey}
							onChange={(e) => setGrokKey(e.target.value)}
							className="pr-12"
							disabled={addGrokKeyMutation.isPending}
						/>
						<button
							type="button"
							onClick={() => setShowKey(!showKey)}
							className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
						>
							{showKey ? <EyeOff size={20} /> : <Eye size={20} />}
						</button>
					</div>

					<div className="flex gap-3">
						<Button
							type="button"
							variant="outline"
							onClick={handleReturn}
							className="flex-1"
							disabled={addGrokKeyMutation.isPending}
						>
							<ArrowLeft size={16} className="mr-2" />
							Return
						</Button>
						<Button
							type="submit"
							className="flex-1 bg-purple-600 hover:bg-purple-700"
							disabled={addGrokKeyMutation.isPending}
						>
							{addGrokKeyMutation.isPending
								? "Adding..."
								: "Add your own Grok key"}
						</Button>
					</div>
				</form>

				<div className="mt-6 text-center">
					<p className="text-xs text-gray-500">
						Your API key is stored securely and only used for your chatbot
						requests.
					</p>
				</div>
			</div>
		</div>
	);
};

export const Route = createFileRoute("/(app)/_chatbot/add-key")({
	component: AddGrokKeyPage,
});
