import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Resource } from "@/features/resources/types";
import { extractYouTubeVideoId } from "@/lib/utils";
import YouTube, { YouTubeProps } from "react-youtube";
import VideoResourceViewer from "./video-resource-viewer";
import VideoResourceViewerMobile from "./video-resource-viewer-mobile";
import { useMediaQuery } from "react-responsive";

export const VideoCard = ({ resource }: { resource: Resource }) => {
	const [isViewerOpen, setIsViewerOpen] = useState(false);
	const isDesktop = useMediaQuery({ minWidth: 1024 });

	const opts: YouTubeProps["opts"] = {
		width: "100%",
		height: "100%",
		playerVars: {
			autoplay: 0,
		},
	};

	const handleCardClick = () => {
		setIsViewerOpen(true);
	};

	return (
		<>
			<Card
				className="overflow-hidden flex gap-y-4 p-4 flex-col justify-between min-w-64 cursor-pointer hover:shadow-lg transition-shadow"
				onClick={handleCardClick}
			>
				<div className="relative h-64 w-full ">
					<YouTube
						className="h-full"
						videoId={extractYouTubeVideoId(resource.url)}
						opts={opts}
					/>
				</div>
				<CardContent className="space-y-1 p-0">
					<h3 className="text-base">{resource.title}</h3>
					<p className="text-xs md:text-sm font-normal text-gray-600">
						{resource.description}
					</p>
				</CardContent>
			</Card>

			{/* Video Resource Viewer */}
			{isDesktop ? (
				<VideoResourceViewer
					resource={resource}
					isOpen={isViewerOpen}
					onClose={() => setIsViewerOpen(false)}
				/>
			) : (
				<VideoResourceViewerMobile
					resource={resource}
					isOpen={isViewerOpen}
					onClose={() => setIsViewerOpen(false)}
				/>
			)}
		</>
	);
};
